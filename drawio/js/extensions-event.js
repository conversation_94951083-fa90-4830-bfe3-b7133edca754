window.addEventListener('DOMContentLoaded', function () {
    var body = document.querySelector('body');
    const search = new URLSearchParams(window.location.search);
    const key = search.get('key');
    let parentRect = {};
    const parent = window.opener || window.parent;

    window.addEventListener('message', function (event) {
        if (event.data && typeof event.data === 'string') {
            try {
                const parsedMessage = JSON.parse(event.data);
                if (parsedMessage.action === 'parentRectMessage') {
                    parentRect = parsedMessage.rect;
                }
            } catch (error) {}
        }
    });

    body.addEventListener('mouseenter', function (event) {
        parent.postMessage(JSON.stringify({ event: 'mouseenter', data: { x: event.x, y: event.y, key: key, parentRect } }), '*');
    });

    body.addEventListener('mouseleave', function (event) {
        parent.postMessage(JSON.stringify({ event: 'mouseleave', data: { x: event.x, y: event.y, key: key, parentRect } }), '*');
    });

    body.addEventListener('click', function (event) {
        parent.postMessage(JSON.stringify({ event: 'click', data: { x: event.x, y: event.y, key: key, parentRect } }), '*');
    });

    body.addEventListener('mousemove', function (event) {
        parent.postMessage(JSON.stringify({ event: 'mousemove', data: { x: event.x, y: event.y, key: key, parentRect } }), '*');
    });

    body.addEventListener('mouseup', function (event) {
        parent.postMessage(JSON.stringify({ event: 'mouseup', data: { x: event.x, y: event.y, key: key, parentRect } }), '*');
    });
});
