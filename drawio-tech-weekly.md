# Draw.io 技术接入周刊

## 项目概述

本项目成功集成了 Draw.io 图形编辑器，为用户提供了强大的在线图表绘制功能。通过自定义的事件处理机制和插件架构，实现了与主应用的无缝集成。

## 技术架构

### 1. Draw.io 源码集成

**目录结构**: `/drawio/`

基于 Draw.io 官方源码进行了本地化部署，主要包含：

- **核心文件**: `index.html` - Draw.io 主入口页面
- **JavaScript 库**: `js/` 目录包含所有核心功能模块
- **样式资源**: `styles/` 目录包含主题和样式文件
- **图标资源**: `images/` 目录包含各类图标和图片资源
- **模板库**: `templates/` 目录包含预设图表模板

### 2. 自定义事件处理机制

**核心文件**: `/drawio/js/extensions-event.js`

为解决项目页面与 iframe 之间的事件阻断问题，新增了专门的事件处理脚本：

#### 主要功能特性

1. **跨框架通信**
   - 通过 `postMessage` API 实现父子窗口通信
   - 支持双向消息传递机制

2. **鼠标事件捕获**
   ```javascript
   // 捕获的事件类型
   - mouseenter: 鼠标进入事件
   - mouseleave: 鼠标离开事件  
   - click: 点击事件
   - mousemove: 鼠标移动事件
   - mouseup: 鼠标释放事件
   ```

3. **位置信息传递**
   - 实时传递鼠标坐标 (x, y)
   - 包含父容器位置信息 (parentRect)
   - 携带唯一标识符 (key) 用于区分不同实例

4. **消息格式标准化**
   ```javascript
   // 标准消息格式
   {
     event: 'eventType',
     data: {
       x: number,
       y: number, 
       key: string,
       parentRect: object
     }
   }
   ```

### 3. Angular 插件模块

**目录结构**: `/common/plugins/drawio/`

#### 核心组件

1. **DrawioComponent** (`drawio.component.ts`)
   - 负责 Draw.io iframe 的渲染和管理
   - 处理与 Draw.io 实例的消息通信
   - 支持图表的显示、编辑和导出功能

2. **DrawioEditor** (`drawio.editor.ts`)
   - 提供编辑器操作接口
   - 支持图表元素的插入、更新和删除
   - 集成富文本编辑器 (Slate.js)

3. **GraphEditorComponent** (`graph-editor/graph-editor.component.ts`)
   - 独立的图表编辑器对话框
   - 支持全屏编辑模式
   - 提供自动保存功能

#### 关键技术实现

1. **安全的 URL 处理**
   ```typescript
   private generateDrawioUrl() {
     const params = new URLSearchParams({
       embed: '1',
       lang: this.translate.currentLang.replace('zh-cn', 'zh'),
       proto: 'json',
       key: this.element.key
     });
     const rawUrl = `${teamBaseUrl}/drawio/index.html?${params.toString()}`;
     this.drawioUrl = this.sanitizer.bypassSecurityTrustResourceUrl(rawUrl);
   }
   ```

2. **消息处理机制**
   ```typescript
   private handleMessage(event: MessageEvent): void {
     if (event.origin !== this.drawioOrigin) return;
     
     const parsedMessage = JSON.parse(event.data);
     switch(parsedMessage.event) {
       case 'init': this.loadDrawio(); break;
       case 'click': this.handleClick(parsedMessage); break;
       case 'mousemove': this.handleMouseMove(parsedMessage); break;
     }
   }
   ```

3. **响应式尺寸调整**
   - 支持拖拽调整图表容器大小
   - 实时同步鼠标位置进行尺寸调整
   - 集成 Angular CDK Resizable 指令

## 技术亮点

### 1. 事件透传机制
通过自定义的 `extensions-event.js`，成功解决了 iframe 内外事件隔离的问题，实现了：
- 鼠标事件的实时捕获和传递
- 精确的坐标位置计算
- 多实例的独立事件处理

### 2. 安全性保障
- 严格的消息来源验证 (`event.origin` 检查)
- URL 参数的安全处理和转义
- 跨站脚本攻击 (XSS) 防护

### 3. 用户体验优化
- 加载状态指示器
- 平滑的动画过渡效果
- 响应式布局适配
- 多语言支持

### 4. 性能优化
- 懒加载机制
- 事件防抖处理
- 内存泄漏防护

## 集成流程

### 1. 初始化流程
```
页面加载 → 生成 Draw.io URL → 创建 iframe → 
监听消息事件 → Draw.io 初始化完成 → 加载图表数据
```

### 2. 编辑流程  
```
用户点击编辑 → 打开编辑器对话框 → 
加载现有数据 → 用户编辑 → 自动保存 → 
用户确认保存 → 更新图表数据 → 关闭编辑器
```

### 3. 导出流程
```
用户点击导出 → 发送导出消息 → 
Draw.io 生成图片 → 返回 Base64 数据 → 
触发文件下载
```

## 技术栈

- **前端框架**: Angular 17+
- **富文本编辑**: Slate.js + Theia Editor
- **图表引擎**: Draw.io (mxGraph)
- **UI 组件库**: ngx-tethys
- **构建工具**: Webpack
- **类型检查**: TypeScript

## 未来规划

1. **功能增强**
   - 支持更多图表模板
   - 增加协作编辑功能
   - 集成版本历史管理

2. **性能优化**
   - 实现图表缓存机制
   - 优化大型图表的渲染性能
   - 减少内存占用

3. **用户体验**
   - 增加快捷键支持
   - 优化移动端适配
   - 提供更多自定义选项

## 总结

通过精心设计的事件处理机制和模块化的插件架构，成功实现了 Draw.io 与主应用的深度集成。该方案不仅解决了 iframe 事件阻断的技术难题，还提供了良好的用户体验和可扩展性，为后续功能扩展奠定了坚实的技术基础。
